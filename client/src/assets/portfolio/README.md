# Portfolio Images Guide

## Folder Structure

Your portfolio images should be organized in the following structure:

```
src/assets/portfolio/
├── manipulation/
│   ├── fantasy-portrait-before.jpg
│   ├── fantasy-portrait-after.jpg
│   ├── surreal-manipulation-before.jpg
│   └── surreal-manipulation-after.jpg
├── matte/
│   ├── digital-matte-before.jpg
│   ├── digital-matte-after.jpg
│   ├── cinematic-matte-before.jpg
│   └── cinematic-matte-after.jpg
└── photo-editing/
    ├── movie-poster-before.jpg
    ├── movie-poster-after.jpg
    ├── fashion-retouching-before.jpg
    └── fashion-retouching-after.jpg
```

## Image Requirements

### File Formats
- **Recommended**: JPG, PNG, WebP
- **Size**: Optimized for web (under 2MB per image)
- **Dimensions**: Recommended 800x600px or similar aspect ratio

### Before/After Pairs
Each project needs two images:
- **Before**: Original/source image or work-in-progress
- **After**: Final edited/manipulated result

## Current Projects Setup

### Manipulation Category
1. **Fantasy Portrait Manipulation**
   - Before: `fantasy-portrait-before.jpg`
   - After: `fantasy-portrait-after.jpg`

2. **Surreal Photo Manipulation**
   - Before: `surreal-manipulation-before.jpg`
   - After: `surreal-manipulation-after.jpg`

### Matte Category
1. **Digital Matte Painting**
   - Before: `digital-matte-before.jpg`
   - After: `digital-matte-after.jpg`

2. **Cinematic Matte Environment**
   - Before: `cinematic-matte-before.jpg`
   - After: `cinematic-matte-after.jpg`

### Photo Editing Category
1. **Movie Poster Design**
   - Before: `movie-poster-before.jpg`
   - After: `movie-poster-after.jpg`

2. **Fashion Photo Retouching**
   - Before: `fashion-retouching-before.jpg`
   - After: `fashion-retouching-after.jpg`

## How to Add Your Images

1. **Place your images** in the appropriate folders with the exact filenames listed above
2. **Update project details** in `Portfolio.jsx` if needed (titles, descriptions, tools)
3. **Add more projects** by editing the `sampleProjects` array in `Portfolio.jsx`

## Adding New Projects

To add more projects, edit the `sampleProjects` array in `src/components/Portfolio.jsx`:

```javascript
{
  id: 7, // Increment the ID
  title: "Your Project Title",
  category: "Manipulation", // or "Matte" or "Poster and Photo Editing"
  beforeImage: "/src/assets/portfolio/category/your-before-image.jpg",
  afterImage: "/src/assets/portfolio/category/your-after-image.jpg",
  description: "Description of your work and techniques used.",
  tools: ["Photoshop", "Other Tools"],
  featured: true // or false
}
```

## Tips

- Use descriptive filenames for better organization
- Keep consistent naming conventions
- Optimize images for web to improve loading times
- Consider using WebP format for better compression
- Test the before/after slider functionality after adding images
