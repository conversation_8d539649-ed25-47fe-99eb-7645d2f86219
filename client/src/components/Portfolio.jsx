import { useState, useEffect, useRef } from 'react';
import { motion, useInView, AnimatePresence } from 'framer-motion';
import { gsap } from 'gsap';

const Portfolio = () => {
  const portfolioRef = useRef(null);
  const isInView = useInView(portfolioRef, { once: true, threshold: 0.2 });
  const [selectedProject, setSelectedProject] = useState(null);
  const [projects, setProjects] = useState([]);
  const [filter, setFilter] = useState('All');

  // Categories for filtering
  const categories = ['All', 'Manipulation', 'Matte', 'Poster and Photo Editing'];

  // Your portfolio projects - replace with your actual images
  const sampleProjects = [
    {
      id: 1,
      title: "Fantasy Portrait Manipulation",
      category: "Manipulation",
      beforeImage: "https://drive.google.com/file/d/1bc7oDF5S52CwoGwGise-mQ03c6-h-iGd/view?usp=drive_link",
      afterImage: "https://drive.google.com/file/d/161yVR18JWjlYBIVuXmaffqZloo91l34N/view?usp=drive_link",
      description: "Creative photo manipulation transforming a portrait into a mystical fantasy character with magical elements.",
      tools: ["Photoshop", "Lightroom"],
      featured: true
    },
    {
      id: 2,
      title: "Digital Matte Painting",
      category: "Matte",
      beforeImage: "/src/assets/portfolio/matte/WhatsApp Image 2025-05-04 at 23.18.21_b96e5f8e.jpg",
      afterImage: "/src/assets/portfolio/matte/WhatsApp Image 2025-05-04 at 23.21.45_fd81f3d9.jpg",
      description: "Stunning digital matte painting creating an otherworldly landscape with atmospheric depth and lighting.",
      tools: ["Photoshop", "Blender"],
      featured: false
    },
    {
      id: 3,
      title: "Movie Poster Design",
      category: "Poster and Photo Editing",
      beforeImage: "/src/assets/portfolio/photo-editing/movie-poster-before.jpg",
      afterImage: "/src/assets/portfolio/photo-editing/movie-poster-after.jpg",
      description: "Professional movie poster design with dramatic lighting, typography, and advanced photo compositing techniques.",
      tools: ["Photoshop", "Illustrator"],
      featured: true
    },
    {
      id: 4,
      title: "Surreal Photo Manipulation",
      category: "Manipulation",
      beforeImage: "/src/assets/portfolio/manipulation/surreal-manipulation-before.jpg",
      afterImage: "/src/assets/portfolio/manipulation/surreal-manipulation-after.jpg",
      description: "Complex surreal manipulation blending multiple elements to create an impossible yet believable scene.",
      tools: ["Photoshop", "After Effects"],
      featured: false
    },

    {
      id: 5,
      title: "Fashion Photo Retouching",
      category: "Poster and Photo Editing",
      beforeImage: "/src/assets/portfolio/photo-editing/fashion-retouching-before.jpg",
      afterImage: "/src/assets/portfolio/photo-editing/fashion-retouching-after.jpg",
      description: "High-end fashion photo retouching with skin enhancement, color grading, and professional finishing touches.",
      tools: ["Photoshop", "Lightroom"],
      featured: false
    }
  ];

  useEffect(() => {
    setProjects(sampleProjects);
  }, []);

  // Filter projects based on selected category
  const filteredProjects = filter === 'All'
    ? projects
    : projects.filter(project => project.category === filter);

  const BeforeAfterSlider = ({ beforeImage, afterImage, title }) => {
    const [sliderPosition, setSliderPosition] = useState(50);
    const [beforeImageError, setBeforeImageError] = useState(false);
    const [afterImageError, setAfterImageError] = useState(false);
    const sliderRef = useRef(null);

    const handleSliderChange = (e) => {
      setSliderPosition(e.target.value);
    };

    // Fallback images for development
    const fallbackBefore = "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=500&h=400&fit=crop";
    const fallbackAfter = "https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=500&h=400&fit=crop";

    return (
      <div className="relative w-full h-64 overflow-hidden rounded-lg cursor-hover">
        {/* Before Image */}
        <img
          src={beforeImageError ? fallbackBefore : beforeImage}
          alt={`${title} - Before`}
          className="absolute inset-0 w-full h-full object-cover"
          onError={() => setBeforeImageError(true)}
        />

        {/* After Image */}
        <div
          className="absolute inset-0 overflow-hidden"
          style={{ clipPath: `inset(0 ${100 - sliderPosition}% 0 0)` }}
        >
          <img
            src={afterImageError ? fallbackAfter : afterImage}
            alt={`${title} - After`}
            className="w-full h-full object-cover"
            onError={() => setAfterImageError(true)}
          />
        </div>

        {/* Slider */}
        <div className="absolute inset-0 flex items-center">
          <input
            ref={sliderRef}
            type="range"
            min="0"
            max="100"
            value={sliderPosition}
            onChange={handleSliderChange}
            className="w-full h-full opacity-0 cursor-pointer"
          />
          
          {/* Slider Line */}
          <div 
            className="absolute top-0 bottom-0 w-1 bg-neon-blue pointer-events-none"
            style={{ left: `${sliderPosition}%` }}
          >
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-neon-blue rounded-full flex items-center justify-center">
              <div className="w-4 h-4 bg-white rounded-full"></div>
            </div>
          </div>
        </div>

        {/* Labels */}
        <div className="absolute top-4 left-4 bg-black bg-opacity-70 px-2 py-1 rounded text-xs text-white">
          BEFORE
        </div>
        <div className="absolute top-4 right-4 bg-black bg-opacity-70 px-2 py-1 rounded text-xs text-white">
          AFTER
        </div>
      </div>
    );
  };

  return (
    <section 
      id="portfolio"
      ref={portfolioRef}
      className="min-h-screen bg-black py-20 px-4"
    >
      <div className="max-w-7xl mx-auto">
        {/* Section Title */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
        >
          <h2 className="text-5xl font-bold font-montserrat mb-4">
            <span className="bg-gradient-to-r from-soft-cyan to-warm-coral bg-clip-text text-transparent">
              My Portfolio
            </span>
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-soft-cyan to-warm-coral mx-auto mb-8"></div>
          <p className="text-soft-gray max-w-2xl mx-auto">
            Explore my collection of photo manipulations, matte paintings, and design work.
            Each project showcases different creative techniques and artistic approaches to visual storytelling.
          </p>
        </motion.div>

        {/* Filter Buttons */}
        <motion.div
          className="flex flex-wrap justify-center gap-4 mb-12"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.3 }}
        >
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setFilter(category)}
              className={`px-6 py-3 rounded-full font-medium transition-all cursor-hover ${
                filter === category
                  ? 'bg-soft-cyan text-black'
                  : 'border border-soft-cyan text-soft-cyan hover:bg-soft-cyan hover:text-black'
              }`}
            >
              {category}
            </button>
          ))}
        </motion.div>

        {/* Projects Grid */}
        <motion.div
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
          layout
        >
          <AnimatePresence>
            {filteredProjects.map((project, index) => (
              <motion.div
                key={project.id}
                layout
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="bg-deep-charcoal rounded-lg overflow-hidden border border-medium-gray hover:border-soft-cyan transition-all cursor-hover"
                onClick={() => setSelectedProject(project)}
              >
                <BeforeAfterSlider 
                  beforeImage={project.beforeImage}
                  afterImage={project.afterImage}
                  title={project.title}
                />
                
                <div className="p-6">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-xl font-semibold text-white">{project.title}</h3>
                    {project.featured && (
                      <span className="px-2 py-1 bg-warm-coral text-black text-xs rounded-full">
                        Featured
                      </span>
                    )}
                  </div>

                  <p className="text-soft-gray text-sm mb-4 line-clamp-2">
                    {project.description}
                  </p>

                  <div className="flex items-center justify-between">
                    <span className="text-soft-cyan text-sm font-medium">
                      {project.category}
                    </span>
                    
                    <div className="flex gap-2">
                      {project.tools.map((tool) => (
                        <span
                          key={tool}
                          className="px-2 py-1 bg-medium-gray text-soft-gray text-xs rounded"
                        >
                          {tool}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        </motion.div>


      </div>
    </section>
  );
};

export default Portfolio;
