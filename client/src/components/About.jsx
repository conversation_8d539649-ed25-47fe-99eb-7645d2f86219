import { useEffect, useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

const About = () => {
  const aboutRef = useRef(null);
  const skillsRef = useRef(null);
  const isInView = useInView(aboutRef, { once: true, threshold: 0.3 });

  const skills = [
    { name: 'After Effects', level: 95, icon: '🎬' },
    { name: 'Motion Graphics', level: 92, icon: '🎭' },
    { name: '2D Animation', level: 88, icon: '🎨' },
    { name: 'Visual Effects', level: 85, icon: '✨' },
    { name: 'Video Editing', level: 90, icon: '🎞️' },
    { name: 'Graphic Design', level: 87, icon: '🎯' }
  ];

  const tools = [
    'Adobe After Effects',
    'Adobe Premiere Pro',
    'Adobe Illustrator',
    'Adobe Photoshop',
    'Cinema 4D',
    'Blender',
    '<PERSON><PERSON>in<PERSON> Resolve',
    'Figma'
  ];

  useEffect(() => {
    if (isInView) {
      // Animate skill bars
      skills.forEach((skill, index) => {
        gsap.to(`.skill-bar-${index}`, {
          width: `${skill.level}%`,
          duration: 1.5,
          delay: index * 0.2,
          ease: "power2.out"
        });
      });
    }
  }, [isInView, skills]);

  return (
    <section 
      id="about"
      ref={aboutRef}
      className="min-h-screen bg-black py-20 px-4"
    >
      <div className="max-w-6xl mx-auto">
        {/* Section Title */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
        >
          <h2 className="text-5xl font-bold font-montserrat mb-4">
            <span className="bg-gradient-to-r from-soft-cyan to-warm-coral bg-clip-text text-transparent">
              About Me
            </span>
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-soft-cyan to-warm-coral mx-auto"></div>
        </motion.div>

        <div className="grid lg:grid-cols-3 gap-16 items-start">
          {/* Profile Image Section */}
          <motion.div
            className="lg:col-span-1 flex justify-center"
            initial={{ opacity: 0, x: -50 }}
            animate={isInView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.3 }}
          >
            <div className="relative group">
              {/* Background decorative elements */}
              <div className="absolute -inset-8 bg-gradient-to-br from-soft-cyan/10 via-transparent to-warm-coral/10 rounded-3xl blur-xl"></div>

              {/* Main image container */}
              <div className="relative">
                {/* Decorative frame */}
                <div className="absolute -inset-6 bg-gradient-to-br from-soft-cyan/20 to-warm-coral/20 rounded-2xl rotate-3 group-hover:rotate-6 transition-transform duration-500"></div>
                <div className="absolute -inset-4 bg-gradient-to-tl from-warm-coral/20 to-soft-cyan/20 rounded-2xl -rotate-2 group-hover:-rotate-4 transition-transform duration-500"></div>

                {/* Profile image */}
                <div className="relative bg-deep-charcoal p-4 rounded-2xl border border-medium-gray/50 backdrop-blur-sm">
                  <img
                    src="/images/profile/anshika-profile.jpg"
                    alt="Anshika Singh - Motion Graphics Designer"
                    className="w-56 h-72 md:w-64 md:h-80 object-cover rounded-xl shadow-2xl profile-glow"
                  />

                  {/* Overlay gradient */}
                  <div className="absolute inset-4 rounded-xl bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>

                  {/* Professional badge */}
                  <div className="absolute top-6 left-6 bg-black/80 backdrop-blur-sm px-3 py-1 rounded-full border border-soft-cyan/30">
                    <span className="text-soft-cyan text-xs font-medium">Motion Designer</span>
                  </div>
                </div>

                {/* Floating badge */}
                <div className="absolute -bottom-4 -right-4 bg-gradient-to-r from-soft-cyan to-warm-coral p-3 rounded-full shadow-lg">
                  <span className="text-black font-bold text-sm">🎬</span>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Bio Section */}
          <motion.div
            className="lg:col-span-2"
            initial={{ opacity: 0, x: 50 }}
            animate={isInView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.5 }}
          >
            <h3 className="text-3xl font-semibold font-poppins mb-6 text-soft-cyan">
              Motion Graphics Designer & Visual Artist
            </h3>

            <div className="space-y-4 text-soft-gray leading-relaxed">
              <p>
                Based in India, I'm a passionate motion graphics designer and visual artist
                with expertise in creating captivating animations and dynamic visual content.
                I specialize in bringing static designs to life through motion and storytelling.
              </p>

              <p>
                My creative journey focuses on crafting engaging motion graphics, 2D animations,
                and visual effects that communicate powerful messages. From brand animations
                to explainer videos, I transform ideas into compelling visual narratives
                that resonate with audiences.
              </p>

              <p>
                With a keen eye for detail and a deep understanding of animation principles,
                I deliver high-quality work that combines technical excellence with artistic
                vision. Every frame is carefully crafted to tell your story effectively.
              </p>
            </div>

            {/* Tools */}
            <div className="mt-8">
              <h4 className="text-xl font-semibold mb-4 text-warm-coral">Tools & Software</h4>
              <div className="flex flex-wrap gap-3">
                {tools.map((tool, index) => (
                  <motion.span
                    key={tool}
                    className="px-3 py-1 bg-gray-800 text-gray-300 rounded-full text-sm border border-gray-700 cursor-hover"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={isInView ? { opacity: 1, scale: 1 } : {}}
                    transition={{ delay: 0.8 + index * 0.1 }}
                    whileHover={{ 
                      borderColor: '#00f5ff',
                      color: '#00f5ff',
                      scale: 1.05 
                    }}
                  >
                    {tool}
                  </motion.span>
                ))}
              </div>
            </div>
          </motion.div>

          {/* Skills Section */}
          <motion.div
            ref={skillsRef}
            initial={{ opacity: 0, x: 50 }}
            animate={isInView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.5 }}
          >
            <h3 className="text-3xl font-semibold font-poppins mb-8 text-warm-coral">
              Skills & Expertise
            </h3>
            
            <div className="space-y-6">
              {skills.map((skill, index) => (
                <motion.div
                  key={skill.name}
                  className="skill-item"
                  initial={{ opacity: 0, y: 20 }}
                  animate={isInView ? { opacity: 1, y: 0 } : {}}
                  transition={{ delay: 0.7 + index * 0.1 }}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-3">
                      <span className="text-2xl">{skill.icon}</span>
                      <span className="font-medium text-white">{skill.name}</span>
                    </div>
                    <span className="text-soft-cyan font-semibold">{skill.level}%</span>
                  </div>
                  
                  <div className="w-full bg-gray-800 rounded-full h-2 overflow-hidden">
                    <div
                      className={`skill-bar-${index} h-full bg-gradient-to-r from-soft-cyan to-warm-coral rounded-full`}
                      style={{ width: '0%' }}
                    />
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Stats */}
            <motion.div
              className="grid grid-cols-3 gap-6 mt-12 p-6 bg-gray-900 rounded-lg border border-gray-800"
              initial={{ opacity: 0, y: 30 }}
              animate={isInView ? { opacity: 1, y: 0 } : {}}
              transition={{ delay: 1.2 }}
            >
              <div className="text-center">
                <div className="text-3xl font-bold text-soft-cyan">100+</div>
                <div className="text-sm text-soft-gray">Projects</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-warm-coral">30+</div>
                <div className="text-sm text-soft-gray">Clients</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-soft-cyan">3+</div>
                <div className="text-sm text-soft-gray">Years</div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default About;
