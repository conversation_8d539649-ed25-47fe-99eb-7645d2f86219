import { motion } from 'framer-motion';
import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';

const Preloader = () => {
  const loaderRef = useRef(null);
  const textRef = useRef(null);

  useEffect(() => {
    const tl = gsap.timeline();
    
    // Glitch animation for the text
    tl.to(textRef.current, {
      duration: 0.1,
      x: -2,
      y: 2,
      repeat: -1,
      yoyo: true,
      ease: "power2.inOut"
    });

    // Rotate the loader
    gsap.to(loaderRef.current, {
      rotation: 360,
      duration: 2,
      repeat: -1,
      ease: "none"
    });

    return () => {
      tl.kill();
    };
  }, []);

  return (
    <motion.div
      className="fixed inset-0 bg-black flex items-center justify-center z-50"
      initial={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="text-center">
        {/* Animated Logo/Initials */}
        <div
          ref={loaderRef}
          className="w-20 h-20 border-4 border-soft-cyan border-t-transparent rounded-full mb-8 mx-auto"
        />
        
        {/* Glitch Text */}
        <div className="relative">
          <h1 
            ref={textRef}
            className="text-4xl font-bold font-montserrat text-white glitch"
            data-text="LOADING"
          >
            LOADING
          </h1>
          
          {/* Animated dots */}
          <div className="flex justify-center mt-4 space-x-2">
            {[0, 1, 2].map((i) => (
              <motion.div
                key={i}
                className="w-3 h-3 bg-soft-cyan rounded-full"
                animate={{
                  scale: [1, 1.5, 1],
                  opacity: [0.5, 1, 0.5]
                }}
                transition={{
                  duration: 1,
                  repeat: Infinity,
                  delay: i * 0.2
                }}
              />
            ))}
          </div>
        </div>

        {/* Progress bar */}
        <div className="w-64 h-1 bg-medium-gray rounded-full mt-8 mx-auto overflow-hidden">
          <motion.div
            className="h-full bg-gradient-to-r from-soft-cyan to-warm-coral rounded-full"
            initial={{ width: 0 }}
            animate={{ width: "100%" }}
            transition={{ duration: 2.5, ease: "easeInOut" }}
          />
        </div>

        {/* Tagline */}
        <motion.p
          className="text-soft-gray mt-4 font-poppins"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1, duration: 0.5 }}
        >
          Bringing Stories to Life Through Motion & Design
        </motion.p>
      </div>
    </motion.div>
  );
};

export default Preloader;
