# How to Add Your Portfolio Images

## Quick Setup Guide

### 1. Prepare Your Images
- **Format**: JPG, PNG, or WebP
- **Size**: Optimize for web (under 2MB each)
- **Dimensions**: Recommended 800x600px or similar aspect ratio
- **Naming**: Use the exact filenames listed below

### 2. Image Placement

Copy your images to these exact locations:

#### Manipulation Projects
```
client/src/assets/portfolio/manipulation/
├── fantasy-portrait-before.jpg      (Original photo)
├── fantasy-portrait-after.jpg       (Your manipulation result)
├── surreal-manipulation-before.jpg  (Source image)
└── surreal-manipulation-after.jpg   (Your surreal creation)
```

#### Matte Painting Projects
```
client/src/assets/portfolio/matte/
├── digital-matte-before.jpg         (Base/reference image)
├── digital-matte-after.jpg          (Your matte painting)
├── cinematic-matte-before.jpg       (Original scene)
└── cinematic-matte-after.jpg        (Your cinematic environment)
```

#### Photo Editing Projects
```
client/src/assets/portfolio/photo-editing/
├── movie-poster-before.jpg          (Raw elements)
├── movie-poster-after.jpg           (Final poster design)
├── fashion-retouching-before.jpg    (Original photo)
└── fashion-retouching-after.jpg     (Retouched result)
```

### 3. Customization Options

#### Update Project Details
Edit `client/src/components/Portfolio.jsx` to customize:
- Project titles
- Descriptions
- Tools used
- Featured status

#### Add More Projects
Add new projects to the `sampleProjects` array:

```javascript
{
  id: 7,
  title: "Your New Project",
  category: "Manipulation", // or "Matte" or "Poster and Photo Editing"
  beforeImage: "/src/assets/portfolio/category/new-before.jpg",
  afterImage: "/src/assets/portfolio/category/new-after.jpg",
  description: "Description of your work...",
  tools: ["Photoshop", "Other Tools"],
  featured: true
}
```

### 4. Testing

1. Add your images to the folders
2. Refresh your browser at `http://localhost:5173/`
3. Navigate to the Portfolio section
4. Test the before/after sliders
5. Try the category filters

### 5. Fallback System

Don't worry if you don't have all images ready yet! The system includes:
- **Fallback images**: Placeholder images will show if your files are missing
- **Error handling**: No broken image icons
- **Graceful degradation**: Portfolio works even with missing images

### 6. Image Optimization Tips

- **Compress images**: Use tools like TinyPNG or ImageOptim
- **Consistent aspect ratios**: Keep similar dimensions for better layout
- **WebP format**: Consider converting to WebP for better compression
- **Alt text**: Images include descriptive alt text automatically

### 7. Next Steps

After adding your images:
1. **Update descriptions** to match your actual work
2. **Adjust project titles** to reflect your style
3. **Add more projects** as needed
4. **Test on different devices** to ensure responsive design
5. **Consider adding project details modal** for more information

Your portfolio will automatically update when you add the images - no code changes needed!
