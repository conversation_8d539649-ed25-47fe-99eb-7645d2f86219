<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON><PERSON><PERSON> - Motion Graphics Designer & Visual Artist</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              'primary-black': '#000000',
              'deep-charcoal': '#0a0a0a',
              'pure-white': '#ffffff',
              'soft-gray': '#a1a1aa',
              'medium-gray': '#52525b',
              'soft-cyan': '#67e8f9',
              'warm-coral': '#fb7185',
            },
            fontFamily: {
              'poppins': ['Poppins', 'sans-serif'],
              'montserrat': ['Montserrat', 'sans-serif'],
            },
            animation: {
              'glitch': 'glitch 0.3s ease-in-out infinite alternate',
              'float': 'float 6s ease-in-out infinite',
              'pulse-glow': 'pulse-glow 2s ease-in-out infinite alternate',
            },
            keyframes: {
              glitch: {
                '0%': { transform: 'translate(0)' },
                '20%': { transform: 'translate(-2px, 2px)' },
                '40%': { transform: 'translate(-2px, -2px)' },
                '60%': { transform: 'translate(2px, 2px)' },
                '80%': { transform: 'translate(2px, -2px)' },
                '100%': { transform: 'translate(0)' },
              },
              float: {
                '0%, 100%': { transform: 'translateY(0px)' },
                '50%': { transform: 'translateY(-20px)' },
              },
              'pulse-glow': {
                '0%': { boxShadow: '0 0 5px #67e8f9, 0 0 10px #67e8f9, 0 0 15px #67e8f9' },
                '100%': { boxShadow: '0 0 10px #67e8f9, 0 0 20px #67e8f9, 0 0 25px #67e8f9' },
              },
            },
          },
        },
      }
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
